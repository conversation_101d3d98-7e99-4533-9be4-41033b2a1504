import pyautogui
import random
import time
from config.config import HUMANIZATION

class InputController:
    """Simulates human-like mouse and keyboard inputs."""
    
    @staticmethod
    def press_key(key):
        """Simulates pressing a key."""
        pyautogui.press(key)
        time.sleep(random.uniform(
            HUMANIZATION["key_delay_min"],
            HUMANIZATION["key_delay_max"]
        ))
    
    @staticmethod
    def click_mouse(x, y):
        """Clicks at position with humanized movement."""
        offset_x = random.randint(-HUMANIZATION["mouse_offset"], HUMANIZATION["mouse_offset"])
        offset_y = random.randint(-HUMANIZATION["mouse_offset"], HUMANIZATION["mouse_offset"])
        pyautogui.moveTo(
            x + offset_x,
            y + offset_y,
            duration=random.uniform(
                HUMANIZATION["mouse_move_duration_min"],
                HUMANIZATION["mouse_move_duration_max"]
            )
        )
        pyautogui.click()