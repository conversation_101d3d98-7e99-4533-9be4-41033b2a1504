import pyautogui
import cv2
import numpy as np
import time
import os

# Optional: pass image path via command line or set it here
image_path = "gameplay.png"  # or set from CLI args: sys.argv[1] if len(sys.argv) > 1 else ""


# Load image from file or take screenshot
if image_path and os.path.isfile(image_path):
    print(f"Loading image from: {image_path}")
    image = cv2.imread(image_path, cv2.IMREAD_COLOR)
else:
    # Wait 10 seconds before screenshotting
    print("Starting in 10 seconds... Move your screen to desired view.")
    time.sleep(10)
    print("Taking screenshot...")
    screenshot = pyautogui.screenshot()
    image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

clone = image.copy()
drawing = False
ix, iy = -1, -1
fx, fy = -1, -1

def draw_rectangle(event, x, y, flags, param):
    global ix, iy, fx, fy, drawing, image

    if event == cv2.EVENT_LBUTTONDOWN:
        drawing = True
        ix, iy = x, y
        fx, fy = x, y

    elif event == cv2.EVENT_MOUSEMOVE and drawing:
        fx, fy = x, y
        image = clone.copy()
        cv2.rectangle(image, (ix, iy), (fx, fy), (0, 255, 0), 2)

    elif event == cv2.EVENT_LBUTTONUP:
        drawing = False
        fx, fy = x, y
        cv2.rectangle(image, (ix, iy), (fx, fy), (0, 255, 0), 2)

cv2.namedWindow("Select Region")
cv2.setMouseCallback("Select Region", draw_rectangle)

print("Click and drag to select region, then press Enter to confirm or Esc to cancel.")

while True:
    cv2.imshow("Select Region", image)
    key = cv2.waitKey(1) & 0xFF
    if key == 13:  # Enter
        break
    if key == 27:  # Esc
        print("Selection cancelled.")
        cv2.destroyAllWindows()
        exit()

cv2.destroyAllWindows()

x1, y1 = min(ix, fx), min(iy, fy)
x2, y2 = max(ix, fx), max(iy, fy)
width = x2 - x1
height = y2 - y1

print(f"Top-left: ({x1}, {y1})")
print(f"Bottom-right: ({x2}, {y2})")
print(f"Region: x={x1}, y={y1}, width={width}, height={height}")
