from config.config import CHARACTERS

class JinshiCombat:
    """Combat logic for <PERSON><PERSON> (healer)."""
    
    @staticmethod
    def get_action(state, char_index):
        """Returns <PERSON><PERSON>’s action based on state."""
        char_state = state["characters"][char_index]
        char_config = CHARACTERS["jinshi"]
        
        # Use heal skill if ready
        if char_state["cooldown"] == 0:
            return {"type": "key_press", "value": char_config["skills"][0]["key"]}
        
        # No attack sequence (healer role)
        return None