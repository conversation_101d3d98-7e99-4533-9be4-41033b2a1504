from config.config import CHARACTERS, KEYS

class JiyanCombat:
    """Combat logic for <PERSON><PERSON> (main DPS)."""
    
    @staticmethod
    def get_action(state, char_index):
        """Returns <PERSON><PERSON>’s action based on state."""
        char_state = state["characters"][char_index]
        char_config = CHARACTERS["jiyan"]
        
        # Priority 1: Use burst (skill_2) if ready
        if char_state["cooldown"] == 0 and char_config["skills"][1]["priority"] == 1:
            return {"type": "key_press", "value": char_config["skills"][1]["key"]}
        
        # Priority 2: Use main skill (skill_1) if ready
        if char_state["cooldown"] == 0:
            return {"type": "key_press", "value": char_config["skills"][0]["key"]}
        
        # Fallback: Attack sequence (skill + 2 basic attacks)
        return {
            "type": "sequence",
            "value": [KEYS["skill_1"], KEYS["attack"], KEYS["attack"]]
        }