import cv2
from config.config import TEAM, REGIONS
from vision.state_classifier import StateClassifier
from vision.element_detection import ElementDetection

class StateMonitor:
    """Tracks game state for multiple characters."""
    
    def __init__(self):
        self.characters = [
            {"health": 0, "cooldown": 0, "outro_ready": False}
            for _ in TEAM["characters"]
        ]
        self.in_combat = False
        self.current_char_index = 0  # Currently active character
        
        # Initialize counters for staggered updates
        self._cooldown_counter = 0
        self._concerto_counter = 0
        self._combat_counter = 0

    def update(self, image, debug_display=None):
        """Updates state based on current screenshot with optimized performance."""
        i = self.current_char_index
        debug_mode = debug_display is not None
        
        # Always update health (critical)
        self.characters[i]["health"] = ElementDetection.detect_health_bar(
            image, debug=debug_mode, debug_image=debug_display)
        
        # Staggered updates for less critical elements
        self._cooldown_counter += 1
        if self._cooldown_counter % 5 == 0:  # Every 5 frames
            self.characters[i]["cooldown"] = ElementDetection.detect_resonance_skill_cooldown(
                image, debug=debug_mode, debug_image=debug_display)
        
        self._concerto_counter += 1
        if self._concerto_counter % 5 == 0:  # Every 5 frames
            self.characters[i]["concerto"] = ElementDetection.detect_concerto_full(
                image, i, debug=debug_mode, debug_image=debug_display)
        
        self._combat_counter += 1
        if self._combat_counter % 10 == 0:  # Every 10 frames
            self.in_combat = StateClassifier.is_in_combat(
                image, debug=debug_mode, debug_image=debug_display)
        
        # Add character indicator to debug display
        if debug_display is not None:
            # Draw active character indicator
            char_names = TEAM["characters"]
            char_text = f"Active: {char_names[i]} ({i+1})"
            cv2.putText(debug_display, char_text, (10, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    def get_state(self):
        """Returns current state."""
        return {
            "characters": self.characters,
            "in_combat": self.in_combat,
            "current_char_index": self.current_char_index,
        }
    
    def set_current_character(self, char_index):
        """Updates the active character."""
        self.current_char_index = char_index
