import importlib
from config.config import TEAM, CHARACTERS, KEYS

class PrioritySystem:
    """Orchestrates team rotations using character-specific combat logic."""

    def __init__(self):
        self.combat_modules = {}
        for char_id in CHARACTERS:
            module_path = CHARACTERS[char_id]["combat_module"]
            module = importlib.import_module(module_path)
            class_name = f"{char_id.capitalize()}Combat"
            self.combat_modules[char_id] = getattr(module, class_name)
    
    def evaluate_condition(self, state, condition, char_index):
        """Checks if a rotation condition is met."""
        char_state = state["characters"][char_index]
        if condition == "cooldown_ready":
            return char_state["cooldown"] == 0
        elif condition == "outro_ready":
            return char_state["outro_ready"]
        # elif condition == "health_low":
        #     return any(c["health"] < TEAM["health_low_threshold"] for c in state["characters"])
        return False
    
    def decide_action(self, state):
        """Returns the next action based on team rotation."""
        if not state["in_combat"]:
            return None
        
        for step in TEAM["rotation"]:
            char_id = step["char"]
            char_index = TEAM["characters"].index(char_id)
            action_type = step["action"]
            condition = step["condition"]

            if (self.evaluate_condition(state, condition, char_index)):
                if action_type == "switch":
                    if char_index != state["current_char_index"]:
                        return {
                            "type": "key_press",
                            "value": KEYS[f"switch_char_{char_index + 1}"],
                            "char_index": char_index,
                        }
                elif action_type == "use_skill":
                    combat_class = self.combat_modules[char_id]
                    action = combat_class.get_action(state, char_index)
                    if action:
                        action["char_index"] = char_index
                        return action

        # Fallback: Current character’s combat logic
        current_char_id = TEAM["characters"][state["current_char_index"]]
        combat_class = self.combat_modules[current_char_id]
        action = combat_class.get_action(state, state["current_char_index"])
        if action:
            action["char_index"] = state["current_char_index"]
        return action