# Configuration settings for the Wuthering Waves automation tool

# Screen regions (x, y, width, height) - adjust based on your game window
REGIONS = {
    "health_bar": (894, 1019, 114, 31),  # Character health bar
    "enemy_health_bar": (500, 50, 200, 20),  # Enemy health bar
    "resonance_skill": (1553, 928, 88, 82),  # resonance skill cooldown
    "concerto_ring": (716, 972, 61, 61),  # resonance skill cooldown
}

# Key bindings
KEYS = {
    "skill_1": "q",  # Primary skill
    "skill_2": "e",  # Secondary skill
    "ultimate": "r",  # Ultimate skill
    "switch_char_1": "1",  # Switch to character 1
    "switch_char_2": "2",  # Switch to character 2
    "switch_char_3": "3",  # Switch to character 3
    "attack": "left",  # Basic attack
}

# Color thresholds (HSV format)
COLOR_THRESHOLDS = {
    "health_red": {
        "lower": (0, 120, 70),
        "upper": (10, 255, 255),
    },
    "concerto_ring": {
        0: ([20, 100, 100], [35, 255, 255]),  # char 0 = yellow
        1: ([100, 150, 50], [130, 255, 255]), # char 1 = blue-ish
        1: ([100, 150, 50], [130, 255, 255]), # char 2 = blue-ish
    }
}

# Detection thresholds
DETECTION = {
    "combat_match_threshold": 0.8,
    "health_bar_min_pixels": 100,
    "template_match_threshold": 0.8,
}

# Humanization settings
HUMANIZATION = {
    "key_delay_min": 0.1,
    "key_delay_max": 0.3,
    "mouse_offset": 10,
    "mouse_move_duration_min": 0.4,
    "mouse_move_duration_max": 0.7,
}

# Character profiles
CHARACTERS = {
    "jiyan": {
        "combat_module": "combat.jiyan",
        "skills": [
            {"key": KEYS["skill_1"], "cooldown": 8.0, "priority": 2},  # Main skill
            {"key": KEYS["skill_2"], "cooldown": 12.0, "priority": 1},  # Burst
        ],
    },
    "yinlin": {
        "combat_module": "combat.yinlin",
        "skills": [
            {"key": KEYS["skill_1"], "cooldown": 10.0, "priority": 1},  # Buff skill
        ],
    },
    "baizhi": {
        "combat_module": "combat.baizhi",
        "skills": [
            {"key": KEYS["skill_1"], "cooldown": 15.0, "priority": 1},  # Heal skill
        ],
    },
}

# Team composition and rotation settings
TEAM = {
    "characters": ["jiyan", "yinlin", "baizhi"],  # Order of characters
    "rotation": [
        {"char": "yinlin", "action": "use_skill", "condition": "cooldown_ready"},  # Sub-DPS buff
        {"char": "jiyan", "action": "switch", "condition": "concerto_full"},  # Switch for outro
        {"char": "jiyan", "action": "use_skill", "condition": "cooldown_ready"},  # DPS skill
        {"char": "baizhi", "action": "switch", "condition": "health_low"},  # Switch to healer
        {"char": "baizhi", "action": "use_skill", "condition": "cooldown_ready"},  # Heal
    ],
    "health_low_threshold": 30.0,
}