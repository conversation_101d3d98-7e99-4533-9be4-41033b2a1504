import pyautogui
import cv2
import numpy as np
import mss
import time

class ScreenCapture:
    """Captures screenshots of the game window or specific regions."""
    
    # Initialize mss once for better performance
    _sct = mss.mss()
    _last_frame_time = 0
    _last_frame = None
    _frame_cache_ms = 16  # ~60fps cache time
    
    @staticmethod
    def capture_full_screen():
        """Captures the entire screen using fast mss library."""
        # Check if we can return cached frame (for multiple calls within same frame time)
        current_time = time.time() * 1000
        if ScreenCapture._last_frame is not None and current_time - ScreenCapture._last_frame_time < ScreenCapture._frame_cache_ms:
            return ScreenCapture._last_frame.copy()
            
        # Capture new frame
        monitor = ScreenCapture._sct.monitors[1]  # Primary monitor
        sct_img = ScreenCapture._sct.grab(monitor)
        
        # Convert to numpy array (faster than using pyautogui)
        img = np.array(sct_img)
        
        # Convert BGRA to BGR (remove alpha channel)
        img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
        
        # Cache the frame
        ScreenCapture._last_frame = img
        ScreenCapture._last_frame_time = current_time
        
        return img
    
    @staticmethod
    def capture_region(x, y, width, height):
        """Captures a specific region of the screen using fast mss library."""
        region = {"top": y, "left": x, "width": width, "height": height}
        sct_img = ScreenCapture._sct.grab(region)
        
        # Convert to numpy array
        img = np.array(sct_img)
        
        # Convert BGRA to BGR
        return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
