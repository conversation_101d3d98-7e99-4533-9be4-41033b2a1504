import time
import keyboard
import cv2
import argparse
import numpy as np
from vision.screen_capture import Screen<PERSON>apture
from decision.state_monitor import StateMonitor
from vision.state_classifier import StateClassifier
from vision.element_detection import ElementDetection
from decision.priority_system import PrioritySystem
from execution.action_sequencer import ActionSequencer

def parse_args():
    parser = argparse.ArgumentParser(description="Wuthering Waves Bot")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--image", type=str, help="Path to debug image")
    parser.add_argument("--live", action="store_true", help="Use live screen capture in debug mode")
    parser.add_argument("--no-vis", action="store_true", help="Disable visualization in debug mode")
    parser.add_argument("--delay", type=float, default=0.5, help="Loop delay in seconds")
    parser.add_argument("--fps-test", action="store_true", help="Run FPS test")
    parser.add_argument("--test-duration", type=int, default=10, help="Duration of FPS test in seconds")
    return parser.parse_args()

def profile_performance():
    """Profile each component to identify bottlenecks."""
    print("\nProfiling performance of individual components...")
    
    # Initialize components
    state_monitor = StateMonitor()
    priority_system = PrioritySystem()
    
    # Capture a single frame for testing
    print("Testing screen capture...")
    start_time = time.time()
    for _ in range(10):
        image = ScreenCapture.capture_full_screen()
    screen_capture_time = (time.time() - start_time) / 10
    print(f"Screen capture: {screen_capture_time:.4f}s ({1/screen_capture_time:.1f} FPS)")
    
    # Test state monitor update
    print("Testing state monitor...")
    start_time = time.time()
    for _ in range(5):
        state_monitor.update(image)
    state_monitor_time = (time.time() - start_time) / 5
    print(f"State monitor: {state_monitor_time:.4f}s ({1/state_monitor_time:.1f} FPS)")
    
    # Test individual detection functions
    print("Testing individual detection functions...")
    
    # Health detection
    start_time = time.time()
    for _ in range(5):
        ElementDetection.detect_health_bar(image)
    health_time = (time.time() - start_time) / 5
    print(f"Health detection: {health_time:.4f}s ({1/health_time:.1f} FPS)")
    
    # Cooldown detection
    start_time = time.time()
    for _ in range(5):
        ElementDetection.detect_resonance_skill_cooldown(image)
    cooldown_time = (time.time() - start_time) / 5
    print(f"Cooldown detection: {cooldown_time:.4f}s ({1/cooldown_time:.1f} FPS)")
    
    # Concerto detection
    start_time = time.time()
    for _ in range(5):
        ElementDetection.detect_concerto_full(image, 0)
    concerto_time = (time.time() - start_time) / 5
    print(f"Concerto detection: {concerto_time:.4f}s ({1/concerto_time:.1f} FPS)")
    
    # Combat detection
    start_time = time.time()
    for _ in range(5):
        StateClassifier.is_in_combat(image)
    combat_time = (time.time() - start_time) / 5
    print(f"Combat detection: {combat_time:.4f}s ({1/combat_time:.1f} FPS)")
    
    # Decision making
    start_time = time.time()
    state = state_monitor.get_state()
    for _ in range(5):
        priority_system.decide_action(state)
    decision_time = (time.time() - start_time) / 5
    print(f"Decision making: {decision_time:.4f}s ({1/decision_time:.1f} FPS)")
    
    # Print summary
    print("\nPerformance Summary:")
    print(f"Screen capture: {screen_capture_time:.4f}s")
    print(f"State monitor: {state_monitor_time:.4f}s")
    print(f"Health detection: {health_time:.4f}s")
    print(f"Cooldown detection: {cooldown_time:.4f}s")
    print(f"Concerto detection: {concerto_time:.4f}s")
    print(f"Combat detection: {combat_time:.4f}s")
    print(f"Decision making: {decision_time:.4f}s")
    
    total_time = screen_capture_time + state_monitor_time
    print(f"\nEstimated maximum FPS: {1/total_time:.1f}")
    print("Bottleneck components (slowest first):")
    components = [
        ("State monitor", state_monitor_time),
        ("Screen capture", screen_capture_time),
        ("Health detection", health_time),
        ("Cooldown detection", cooldown_time),
        ("Concerto detection", concerto_time),
        ("Combat detection", combat_time),
        ("Decision making", decision_time)
    ]
    components.sort(key=lambda x: x[1], reverse=True)
    for component, time_taken in components:
        print(f"- {component}: {time_taken:.4f}s ({1/time_taken:.1f} FPS)")

def run_fps_test(duration=10, debug_visualization=True):
    """Run a performance test to measure FPS."""
    print(f"\nStarting FPS test for {duration} seconds...")
    
    # First run the profiler to identify bottlenecks
    profile_performance()
    
    # Initialize components
    state_monitor = StateMonitor()
    priority_system = PrioritySystem()
    action_sequencer = ActionSequencer(state_monitor)
    
    # Create debug window if visualization is enabled
    if debug_visualization:
        cv2.destroyAllWindows()
        cv2.namedWindow("FPS Test", cv2.WINDOW_NORMAL)
        cv2.resizeWindow("FPS Test", 1280, 720)
    
    # Performance tracking variables
    frame_count = 0
    start_time = time.time()
    fps_history = []
    
    # Enable OpenCV optimizations
    cv2.setUseOptimized(True)
    if hasattr(cv2, 'setNumThreads'):
        cv2.setNumThreads(8)  # Use multiple CPU cores
    
    # Run the test for the specified duration
    while time.time() - start_time < duration:
        # Capture frame
        frame_start = time.time()
        image = ScreenCapture.capture_full_screen()
        
        # Create debug display only if needed
        debug_display = image.copy() if debug_visualization else None
        
        # Process frame
        state_monitor.update(image, debug_display=debug_display)
        state = state_monitor.get_state()
        action = priority_system.decide_action(state)
        
        # Calculate frame processing time
        frame_time = time.time() - frame_start
        current_fps = 1.0 / frame_time if frame_time > 0 else 0
        fps_history.append(current_fps)
        
        # Update frame count
        frame_count += 1
        elapsed = time.time() - start_time
        avg_fps = frame_count / elapsed if elapsed > 0 else 0
        
        # Display FPS information (only update every 3 frames to reduce overhead)
        if debug_display is not None and frame_count % 3 == 0:
            # Add FPS counter
            cv2.putText(debug_display, f"Current FPS: {current_fps:.1f}", (10, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            cv2.putText(debug_display, f"Average FPS: {avg_fps:.1f}", (10, 60), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            cv2.putText(debug_display, f"Time Remaining: {max(0, duration - elapsed):.1f}s", (10, 90), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            
            # Show visualization
            cv2.imshow("FPS Test", debug_display)
            cv2.waitKey(1)
        
        # Print progress less frequently
        if frame_count % 30 == 0:
            print(f"Processed {frame_count} frames. Current FPS: {current_fps:.1f}, Average FPS: {avg_fps:.1f}")
    
    # Calculate final statistics
    end_time = time.time()
    total_time = end_time - start_time
    final_avg_fps = frame_count / total_time if total_time > 0 else 0
    
    # Calculate percentiles
    fps_history.sort()
    p1 = fps_history[int(len(fps_history) * 0.01)] if fps_history else 0
    p5 = fps_history[int(len(fps_history) * 0.05)] if fps_history else 0
    p50 = fps_history[int(len(fps_history) * 0.5)] if fps_history else 0
    p95 = fps_history[int(len(fps_history) * 0.95)] if fps_history else 0
    p99 = fps_history[int(len(fps_history) * 0.99)] if fps_history else 0
    
    # Print results
    print("\n===== FPS TEST RESULTS =====")
    print(f"Test Duration: {total_time:.2f} seconds")
    print(f"Frames Processed: {frame_count}")
    print(f"Average FPS: {final_avg_fps:.2f}")
    print(f"FPS Percentiles:")
    print(f"  1st: {p1:.2f}")
    print(f"  5th: {p5:.2f}")
    print(f" 50th: {p50:.2f} (median)")
    print(f" 95th: {p95:.2f}")
    print(f" 99th: {p99:.2f}")
    print("===========================\n")
    
    # Clean up
    if debug_visualization:
        cv2.destroyAllWindows()
    
    return {
        "duration": total_time,
        "frames": frame_count,
        "avg_fps": final_avg_fps,
        "percentiles": {
            "p1": p1,
            "p5": p5,
            "p50": p50,
            "p95": p95,
            "p99": p99
        }
    }

def main():
    """Main loop for the bot."""
    args = parse_args()
    
    # Debug configuration
    debug_mode = args.debug
    debug_image_path = args.image if args.image else "gameplay3.png"
    debug_live_capture = args.live
    debug_visualization = not args.no_vis
    loop_delay = args.delay
    
    # FPS test mode
    if args.fps_test:
        run_fps_test(duration=args.test_duration, debug_visualization=debug_visualization)
        return
    
    # Initialize components
    state_monitor = StateMonitor()
    priority_system = PrioritySystem()
    action_sequencer = ActionSequencer(state_monitor)

    print("Bot started. Press 'q' to stop.")
    
    # Load debug image if needed
    static_debug_image = None
    if debug_mode and not debug_live_capture and debug_image_path:
        static_debug_image = cv2.imread(debug_image_path)
        if static_debug_image is None:
            print(f"[ERROR] Could not load debug image from {debug_image_path}")
            return
        print(f"Loaded debug image: {debug_image_path}")

    # Create debug window if visualization is enabled
    if debug_visualization:
        # Close any existing windows first to prevent duplicates
        cv2.destroyAllWindows()
        cv2.namedWindow("Debug View", cv2.WINDOW_NORMAL)
        cv2.resizeWindow("Debug View", 1280, 720)

    frame_count = 0
    start_time = time.time()
    
    # Special case: Static image debug mode (run only once)
    if debug_mode and not debug_live_capture and static_debug_image is not None:
        image = static_debug_image.copy()
        debug_display = image.copy() if debug_visualization else None
        
        # Process the static image once
        state_monitor.update(image, debug_display=debug_display)
        state = state_monitor.get_state()
        action = priority_system.decide_action(state)
        
        # Add state summary to debug display
        if debug_display is not None:
            h, w = debug_display.shape[:2]
            for i, char in enumerate(state["characters"]):
                char_info = f"Char {i+1}: HP={char['health']:.1f}%, CD={char['cooldown']}s, Concerto={char.get('concerto', False)}"
                cv2.putText(debug_display, char_info, (10, h-20-(i*20)), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            
            # Show debug visualization
            cv2.imshow("Debug View", debug_display)
            print("\nStatic image analysis complete. Press any key to exit or 's' to save the debug image.")
            
            key = cv2.waitKey(0)  # Wait indefinitely for a key press
            if key == ord('s'):
                # Save debug frame
                timestamp = int(time.time())
                filename = f"debug_frame_{timestamp}.png"
                cv2.imwrite(filename, debug_display)
                print(f"Saved debug frame to {filename}")
        
        # Print state information to console
        print("\n--- Static Image Analysis ---")
        for i, char in enumerate(state["characters"]):
            print(f"Char {i+1}: Health={char['health']:.2f}%, Cooldown={char['cooldown']}s, Concerto={char.get('concerto', False)}")
        print(f"In Combat: {state['in_combat']}, Active Char: {state['current_char_index']+1}")
        print(f"Recommended Action: {action}")
        print("------------------\n")
        
        # Clean up
        if debug_visualization:
            cv2.destroyAllWindows()
        print("Static image analysis complete.")
        return
    
    # Main loop for live capture or production mode
    while not keyboard.is_pressed("q"):
        frame_count += 1
        
        # Capture image (live debug or production)
        image = ScreenCapture.capture_full_screen()
        
        # Create a copy for visualization if needed
        debug_display = image.copy() if debug_visualization else None
        
        # Update state with debug visualization if enabled
        state_monitor.update(image, debug_display=debug_display)
        state = state_monitor.get_state()
        action = priority_system.decide_action(state)
        
        # Only execute actions in production mode
        if not debug_mode:
            action_sequencer.execute_action(action)

        # Add FPS counter and timestamp to debug display
        if debug_display is not None:
            elapsed = time.time() - start_time
            fps = frame_count / elapsed if elapsed > 0 else 0
            cv2.putText(debug_display, f"FPS: {fps:.1f}", (10, 90), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            cv2.putText(debug_display, f"Time: {elapsed:.1f}s", (10, 110), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            
            # Add state summary at the bottom
            h, w = debug_display.shape[:2]
            for i, char in enumerate(state["characters"]):
                char_info = f"Char {i+1}: HP={char['health']:.1f}%, CD={char['cooldown']}s, Concerto={char.get('concerto', False)}"
                cv2.putText(debug_display, char_info, (10, h-20-(i*20)), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            
            # Show debug visualization
            cv2.imshow("Debug View", debug_display)
            key = cv2.waitKey(1)
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Save current debug frame
                timestamp = int(time.time())
                filename = f"debug_frame_{timestamp}.png"
                cv2.imwrite(filename, debug_display)
                print(f"Saved debug frame to {filename}")

        # Print state information to console
        if debug_mode:
            print("\n--- State Update ---")
            for i, char in enumerate(state["characters"]):
                print(f"Char {i+1}: Health={char['health']:.2f}%, Cooldown={char['cooldown']}s, Concerto={char.get('concerto', False)}")
            print(f"In Combat: {state['in_combat']}, Active Char: {state['current_char_index']+1}")
            print(f"Recommended Action: {action}")
            print("------------------\n")

        time.sleep(loop_delay)

    # Clean up
    if debug_visualization:
        cv2.destroyAllWindows()  # This will close all OpenCV windows including the mask debug window
    print("Bot stopped.")

if __name__ == "__main__":
    main()
