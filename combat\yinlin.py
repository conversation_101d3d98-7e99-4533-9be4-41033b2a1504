from config.config import CHARACTERS

class YinlinCombat:
    """Combat logic for Yin<PERSON> (sub-DPS/support)."""
    
    @staticmethod
    def get_action(state, char_index):
        """Returns <PERSON><PERSON>’s action based on state."""
        char_state = state["characters"][char_index]
        char_config = CHARACTERS["yinlin"]
        
        # Use buff skill if ready
        if char_state["cooldown"] == 0:
            return {"type": "key_press", "value": char_config["skills"][0]["key"]}
        
        # No attack sequence (support role, minimal field time)
        return None