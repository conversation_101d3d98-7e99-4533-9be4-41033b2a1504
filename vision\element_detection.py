import cv2
import numpy as np
from config.config import REGIONS, COLOR_THRESHOLDS

class ElementDetection:
    """Detects UI elements like health bars and text."""

    @staticmethod
    def detect_health_bar(image, debug=False, debug_image=None):
        """Detects health bar percentage using fast color detection."""
        x, y, w, h = REGIONS['health_bar']
        region = image[y:y+h, x:x+w]
        
        if debug and debug_image is not None:
            draw_debug_box(debug_image, x, y, w, h, "Health")

        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)
        
        # Detect red health bar
        lower_red = np.array(COLOR_THRESHOLDS["health_red"]["lower"])
        upper_red = np.array(COLOR_THRESHOLDS["health_red"]["upper"])
        mask = cv2.inRange(hsv, lower_red, upper_red)
        
        # Calculate health percentage based on filled pixels
        total_pixels = w * h
        filled_pixels = cv2.countNonZero(mask)
        health_percent = (filled_pixels / total_pixels) * 100 if total_pixels > 0 else 0
        
        # Scale the percentage to match the actual health bar
        health_percent = min(100, health_percent * 3)  # Adjust multiplier as needed
        
        if debug and debug_image is not None:
            cv2.putText(debug_image, f"Health: {health_percent:.1f}%", 
                       (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        return health_percent

    @staticmethod
    def detect_resonance_skill_cooldown(image, debug=False, debug_image=None):
        """Detects cooldown timer using fast pixel analysis."""
        x, y, w, h = REGIONS['resonance_skill']
        region = image[y:y+h, x:x+w]
        
        if debug and debug_image is not None:
            draw_debug_box(debug_image, x, y, w, h, "Cooldown")

        # Convert to grayscale for faster processing
        gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        
        # Check if the cooldown is visible (bright numbers)
        _, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
        white_pixels = cv2.countNonZero(thresh)
        
        # If there are enough white pixels, there's probably a cooldown number
        if white_pixels > 50:
            # Estimate cooldown based on white pixel count
            cooldown = 5  # Default estimate
        else:
            cooldown = 0  # No cooldown
        
        if debug and debug_image is not None:
            cv2.putText(debug_image, f"CD: {cooldown}s", 
                       (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        return cooldown

    @staticmethod
    def detect_concerto_full(image, char_index, debug=False, debug_image=None):
        """Detects if concerto ring is full using fast color detection."""
        x, y, w, h = REGIONS['concerto_ring']
        region = image[y:y+h, x:x+w]
        
        if debug and debug_image is not None:
            draw_debug_box(debug_image, x, y, w, h, f"Concerto {char_index}")

        # Use pre-defined HSV ranges
        lower_hsv, upper_hsv = COLOR_THRESHOLDS["concerto_ring"].get(
            char_index, ([20, 100, 100], [35, 255, 255])
        )

        # Convert to HSV
        hsv = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)
        color_mask = cv2.inRange(hsv, np.array(lower_hsv), np.array(upper_hsv))
        
        # Simplified detection - just count colored pixels
        colored_pixels = cv2.countNonZero(color_mask)
        total_pixels = w * h
        fill_ratio = colored_pixels / total_pixels if total_pixels > 0 else 0
        
        is_full = fill_ratio > 0.3  # Adjust threshold as needed
        
        if debug and debug_image is not None:
            status = "Ready" if is_full else f"{fill_ratio:.0%}"
            color = (0, 255, 0) if is_full else (0, 165, 255)
            cv2.putText(debug_image, f"Concerto: {status}", 
                       (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return is_full

def draw_debug_box(image, x, y, w, h, label="", color=(0, 255, 0)):
    """Draw a simple debug box without any expensive operations."""
    cv2.rectangle(image, (x, y), (x + w, y + h), color, 2)
    if label:
        cv2.putText(image, label, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
