from execution.input_controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from decision.state_monitor import StateMonitor

class ActionSequencer:
    """Executes sequences of actions like skill chains or switches."""
    
    def __init__(self, state_monitor: StateMonitor):
       self.state_monitor = state_monitor

    def execute_action(self, action):
        """Executes a single action or sequence."""
        if not action:
            return
        
        char_index = action.get("char_index")
        if char_index is not None:
            self.state_monitor.set_current_character(char_index)

        if action["type"] == "key_press":
            InputController.press_key(action["value"])
        elif action["type"] == "sequence":
            for key in action["value"]:
                InputController.press_key(key)