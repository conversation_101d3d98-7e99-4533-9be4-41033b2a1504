import cv2
import numpy as np
from config.config import DETECTION

class StateClassifier:
    """Classifies the game state (combat, menu, etc.)."""
    
    @staticmethod
    def is_in_combat(image, debug=False, debug_image=None):
        """Detects combat state by checking for combat indicators."""
        # Use a faster method than template matching
        # Check for the yellow combat indicator in a specific region
        x, y, w, h = 50, 50, 100, 100  # Adjust this region based on where the combat indicator appears
        region = image[y:y+h, x:x+w]
        
        # Convert to HSV for color detection
        hsv = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)
        
        # Yellow color range for combat indicator
        lower_yellow = np.array([20, 100, 100])
        upper_yellow = np.array([35, 255, 255])
        mask = cv2.inRange(hsv, lower_yellow, upper_yellow)
        
        # Check if enough yellow pixels are found
        yellow_pixels = cv2.countNonZero(mask)
        in_combat = yellow_pixels > 100  # Adjust threshold as needed
        
        if debug and debug_image is not None:
            color = (0, 255, 0) if in_combat else (0, 0, 255)
            cv2.rectangle(debug_image, (x, y), (x + w, y + h), color, 2)
            
            # Add combat status to top of screen
            status = "IN COMBAT" if in_combat else "NOT IN COMBAT"
            cv2.putText(debug_image, status, (10, 60), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        return in_combat
